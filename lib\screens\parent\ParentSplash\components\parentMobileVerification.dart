import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:seawork/components/commonWidgets/customText.dart';
import 'package:seawork/screens/public/login/otp.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:seawork/screens/public/login/loginProvider/loginProvider.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:seawork/screens/public/registration/provider/registrationProvider.dart';
import 'package:seawork/screens/public/login/login.dart';
import 'package:seawork/utils/style/colors.dart';
import 'package:seawork/components/widget/countryCodeDropdown.dart';
import 'package:seawork/models/countryCodeModel.dart';
import 'package:seawork/constants/constantTexts.dart';
import 'package:go_router/go_router.dart';
class ParentMobileVerificationScreen extends ConsumerStatefulWidget {
  final bool forRegistration;

  const ParentMobileVerificationScreen(
      {super.key, this.forRegistration = false});

  @override
  ConsumerState<ParentMobileVerificationScreen> createState() =>
      _ParentMobileVerificationScreenState();
}

class _ParentMobileVerificationScreenState
    extends ConsumerState<ParentMobileVerificationScreen> {
  final TextEditingController phoneController = TextEditingController();
  // NEW ───────────────────────────────────────────────
  bool _isRequestingOtp = false;
  CountryCode _selectedCountryCode = CountryCodeData.defaultCountry;
  // ───────────────────────────────────────────────────
  @override
  Widget build(BuildContext context) {
    final otpState = ref.watch(
        widget.forRegistration ? sendSignUpOtpProvider : generateOtpProvider);
    // keep the flag in sync with provider
    _isRequestingOtp = otpState.isLoading;
    return Scaffold(
      body: Stack(
        children: [
          // Background Image
          Positioned.fill(
            child: SvgPicture.asset(
              'assets/images/Rectangle 3463991 (1).svg',
              fit: BoxFit.cover,
            ),
          ),

          // Dark overlay
          Positioned.fill(
            child: Container(
              color: Colors.black.withOpacity(0.4),
            ),
          ),

          // Main content
          Center(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Logo
                  SvgPicture.asset(
                    'assets/images/logoz.svg',
                    height: 100,
                    width: 100,
                  ),
                  const SizedBox(height: 40),

                  // Verification container
                  Container(
                    padding: const EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      color: Colors.black.withOpacity(0.6),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        // Title
                        DmSansText(
                          "Let's confirm it's you!",
                          fontSize: 24,
                          fontWeight: FontWeight.w600,
                          color: AppColors.whiteColor,
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 16),

                        // Subtitle
                        DmSansText(
                          'Please verify your\nmobile number to continue',
                          fontSize: 14,
                          fontWeight: FontWeight.w400,
                          color: AppColors.whiteColor,
                          textAlign: TextAlign.center,
                        ),

                        const SizedBox(height: 30),
                        Column(
                          children: [
                            DmSansText(
                              'Enter your mobile number',
                              fontSize: 12,
                              fontWeight: FontWeight.w400,
                              color: AppColors.whiteColor,
                            ),
                          ],
                        ),
                        const SizedBox(height: 24),
                        // Mobile input field
                        Row(
                          children: [
                            // Country code dropdown
                            CountryCodeDropdown(
                              initialValue: _selectedCountryCode,
                              onChanged: (CountryCode newCountryCode) {
                                setState(() {
                                  _selectedCountryCode = newCountryCode;
                                });
                              },
                              backgroundColor: Colors.white.withOpacity(0.2),
                              textColor: AppColors.whiteColor,
                              fontSize: 16,
                            ),

                            // Space between country code and input field
                            const SizedBox(width: 8),

                            // Mobile number input field
                            Expanded(
                              child: Container(
                                padding:
                                    const EdgeInsets.symmetric(horizontal: 16),
                                decoration: BoxDecoration(
                                  color: Colors.white.withOpacity(0.2),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: TextField(
                                  controller: phoneController,
                                  style: const TextStyle(
                                      color: AppColors.whiteColor),
                                  decoration: InputDecoration(
                                    hintText: 'Enter mobile number',
                                    hintStyle: TextStyle(
                                      color:
                                          AppColors.whiteColor.withOpacity(0.5),
                                    ),
                                    border: InputBorder.none,
                                  ),
                                  keyboardType: TextInputType.phone,
                                ),
                              ),
                            ),
                          ],
                        ),

                        const SizedBox(height: 8),

                        // Email option
                        Row(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            TextButton(
                              onPressed: () {
                                context.push(
                                  '/parent-email-verification',
                                  extra: {'forRegistration': widget.forRegistration},
                                );
                              },
                              child: DmSansText(
                                'Use Email-ID',
                                fontSize: 14,
                                fontWeight: FontWeight.w600,
                                color: AppColors.whiteColor,
                                underline: true,
                              ),
                            ),
                          ],
                        ),

                        const SizedBox(height: 30),

                        // Get OTP button
                        SizedBox(
                          width: double.infinity,
                          child: ElevatedButton(
                            // ↓ disable while loading
                            onPressed: _isRequestingOtp
                                ? null
                                : () async {
                                    final phoneNumber =
                                        phoneController.text.trim();
                                    if (phoneNumber.isEmpty) {
                                      Fluttertoast.showToast(
                                          msg: ConstantTexts.pleaseEnterPhoneNumber);
                                      return;
                                    }

                                    // Validate phone number for selected country
                                    if (!CountryCodeData.isValidPhoneNumber(phoneNumber, _selectedCountryCode.code)) {
                                      String errorMessage = _selectedCountryCode.code == ConstantTexts.indiaCountryCode
                                          ? ConstantTexts.pleaseEnterValidTenDigitIndianPhone
                                          : ConstantTexts.pleaseEnterValidNineDigitUaePhone;
                                      Fluttertoast.showToast(msg: errorMessage);
                                      return;
                                    }

                                    // Format phone number with selected country code
                                    final formattedPhoneNumber = CountryCodeData.formatPhoneNumber(phoneNumber, _selectedCountryCode.code);

                                    // flip flag → button greyed out immediately
                                    setState(() => _isRequestingOtp = true);
                                    try {
                                      if (widget.forRegistration) {
                                        await ref
                                            .read(
                                                sendSignUpOtpProvider.notifier)
                                            .sendOtp(formattedPhoneNumber);
                                        final state =
                                            ref.read(sendSignUpOtpProvider);
                                        state.when(
                                          data: (data) async {
                                            final error =
                                                data['error']?.toString() ?? '';
                                            final requestId =
                                                data['requestId']?.toString() ??
                                                    '';
                                            final otp =
                                                data['randomNo']?.toString() ??
                                                    '';
                                            if (error ==
                                                'User already exist with same mobile no') {
                                              // Show BottomSheet
                                              showModalBottomSheet(
                                                context: context,
                                                shape:
                                                    const RoundedRectangleBorder(
                                                  borderRadius:
                                                      BorderRadius.vertical(
                                                          top: Radius.circular(
                                                              24)),
                                                ),
                                                isScrollControlled: true,
                                                builder: (context) {
                                                  return Padding(
                                                    padding:
                                                        const EdgeInsets.only(
                                                            left: 16,
                                                            right: 16,
                                                            top: 24,
                                                            bottom: 24),
                                                    child: Column(
                                                      mainAxisSize:
                                                          MainAxisSize.min,
                                                      children: [
                                                        const Text(
                                                          'An account already exists with this number',
                                                          style: TextStyle(
                                                            color: Colors.red,
                                                            fontWeight:
                                                                FontWeight.bold,
                                                            fontSize: 16,
                                                          ),
                                                        ),
                                                        const SizedBox(
                                                            height: 24),
                                                        SizedBox(
                                                          width:
                                                              double.infinity,
                                                          child: ElevatedButton(
                                                            style:
                                                                ElevatedButton
                                                                    .styleFrom(
                                                              backgroundColor:
                                                                  AppColors
                                                                      .viewColor,
                                                              foregroundColor:
                                                                  AppColors
                                                                      .whiteColor,
                                                              padding:
                                                                  const EdgeInsets
                                                                      .symmetric(
                                                                      vertical:
                                                                          16),
                                                              shape:
                                                                  RoundedRectangleBorder(
                                                                borderRadius:
                                                                    BorderRadius
                                                                        .circular(
                                                                            8),
                                                              ),
                                                            ),
                                                            onPressed: () {
                                                              Navigator.pop(
                                                                  context); // Close bottom sheet
                                                              Navigator
                                                                  .pushReplacement(
                                                                context,
                                                                MaterialPageRoute(
                                                                  builder:
                                                                      (context) =>
                                                                          Login(
                                                                              // You may want to pass the number to Login screen
                                                                              ),
                                                                ),
                                                              );
                                                            },
                                                            child: DmSansText(
                                                              'Login to existing account',
                                                              fontSize: 16,
                                                              fontWeight:
                                                                  FontWeight
                                                                      .bold,
                                                              color: AppColors
                                                                  .whiteColor,
                                                            ),
                                                          ),
                                                        ),
                                                        const SizedBox(
                                                            height: 12),
                                                        SizedBox(
                                                          width:
                                                              double.infinity,
                                                          child: OutlinedButton(
                                                            style:
                                                                OutlinedButton
                                                                    .styleFrom(
                                                              side: const BorderSide(
                                                                  color: AppColors
                                                                      .viewColor),
                                                              padding:
                                                                  const EdgeInsets
                                                                      .symmetric(
                                                                      vertical:
                                                                          16),
                                                              shape:
                                                                  RoundedRectangleBorder(
                                                                borderRadius:
                                                                    BorderRadius
                                                                        .circular(
                                                                            8),
                                                              ),
                                                            ),
                                                            onPressed: () {
                                                              Navigator.pop(
                                                                  context); // Just close bottom sheet
                                                            },
                                                            child: DmSansText(
                                                              'Try a different number',
                                                              fontSize: 16,
                                                              fontWeight:
                                                                  FontWeight
                                                                      .bold,
                                                              color: AppColors
                                                                  .viewColor,
                                                            ),
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                  );
                                                },
                                              );
                                            } else if (error.isEmpty &&
                                                requestId.isNotEmpty) {
                                              // OTP sent successfully
                                              Fluttertoast.showToast(
                                                msg: 'Request ID: $requestId',
                                                toastLength: Toast.LENGTH_LONG,
                                                timeInSecForIosWeb: 10,
                                              );
                                              // Optionally, use a Timer to hide after 5 seconds
                                              Future.delayed(
                                                  const Duration(seconds: 10),
                                                  () {
                                                Fluttertoast.cancel();
                                              });
                                              Navigator.push(
                                                context,
                                                MaterialPageRoute(
                                                  builder: (context) =>
                                                      OtpScreen(
                                                    phoneNumber: formattedPhoneNumber,
                                                    otp: otp,
                                                    forRegistrationPhone: true,
                                                    requestId: requestId,
                                                  ),
                                                ),
                                              );
                                            } else {
                                              Fluttertoast.showToast(
                                                  msg: error.isNotEmpty
                                                      ? error
                                                      : 'Failed to get OTP');
                                            }
                                          },
                                          loading: () => Fluttertoast.showToast(
                                              msg: 'Sending OTP...'),
                                          error: (e, _) =>
                                              Fluttertoast.showToast(
                                                  msg: 'Error: $e'),
                                        );
                                      } else {
                                        // Login flow (existing logic)
                                        await ref
                                            .read(generateOtpProvider.notifier)
                                            .generateOtp(formattedPhoneNumber);
                                        final state =
                                            ref.read(generateOtpProvider);
                                        state.when(
                                          data: (data) {
                                            final otp =
                                                data['randomNo']?.toString() ??
                                                    '';
                                            if (otp.isNotEmpty) {
                                              Fluttertoast.showToast(
                                                  msg: 'OTP: $otp',
                                                  toastLength:
                                                      Toast.LENGTH_LONG);
                                              Future.delayed(
                                                  const Duration(minutes: 1),
                                                  () {
                                                Fluttertoast.cancel();
                                              });
                                              Navigator.push(
                                                context,
                                                MaterialPageRoute(
                                                  builder: (context) =>
                                                      OtpScreen(
                                                    phoneNumber: formattedPhoneNumber,
                                                    otp: otp,
                                                    forRegistrationPhone: false,
                                                  ),
                                                ),
                                              );
                                            } else {
                                              Fluttertoast.showToast(
                                                  msg: 'Failed to get OTP');
                                            }
                                          },
                                          loading: () => Fluttertoast.showToast(
                                              msg: 'Sending OTP...'),
                                          error: (e, _) =>
                                              Fluttertoast.showToast(
                                                  msg: 'Error: $e'),
                                        );
                                      }
                                    } finally {
                                      // provider.emit() may still be filling the toast,
                                      // but we re-enable the button once state no longer loading
                                      setState(() => _isRequestingOtp = false);
                                    }
                                  },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: _isRequestingOtp
                                  ? AppColors.lightGreyColor2
                                  : AppColors.whiteColor,
                              foregroundColor: AppColors.blackColor,
                              padding: const EdgeInsets.symmetric(vertical: 16),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                              elevation: 0,
                            ),
                            child: _isRequestingOtp
                                ? const SizedBox(
                                    width: 18,
                                    height: 18,
                                    child: CircularProgressIndicator(
                                        strokeWidth: 2),
                                  )
                                : DmSansText(
                                    'Get OTP',
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                    color: AppColors.blackColor,
                                  ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
