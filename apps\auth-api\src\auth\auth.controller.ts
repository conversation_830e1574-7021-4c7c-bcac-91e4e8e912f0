import { <PERSON>, Get, Param, Query, Req, Res } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Response } from 'express';
import * as jwt from 'jsonwebtoken';
import { AuthService } from './auth.service';
@Controller('auth')
export class AuthController {
  constructor(
    private readonly authService: AuthService,
    private readonly configService: ConfigService,
  ) {}

  // @Post('office365/callback')
  // async office365Login(@Body() authDto: Office365AuthDto) {
  //   try {
  //     const result = await this.authService.handleOffice365Login(authDto);
  //     return {
  //       success: true,
  //       data: result,
  //     };
  //   } catch (error) {
  //     throw new UnauthorizedException(error);
  //   }
  // }

  @Get('login')
  login(
    @Res() res: Response,
    @Req() req: any,
    @Query('codeVerifier') codeVerifier: string,
    @Query('redirect') redirect: string,
  ): void {
    const clientId = this.configService.get<string>('OFFICE_CLIENT_ID');
    const tenantId = this.configService.get<string>('OFFICE_TENANT_ID');
    const redirectUri =
      this.configService.get<string>('REDIRECT_URI') ??
      req.protocol + '://' + req.get('host') + '/auth/callback';
    // const { codeVerifier, codeChallenge } =
    //   this.authService.generateCodeChallengeAndVerifier();
    if (!codeVerifier) {
      res.status(400).send('Code verifier is missing. Please try again.');
      return;
    }
    if (!redirect) {
      res.status(400).send('Redirect is missing. Please try again.');
      return;
    }
    req.session.redirect = redirect;

    req.session.codeVerifier = codeVerifier;
    console.log('codeVerifier', codeVerifier);

    const authorizationUrl =
      `https://login.microsoftonline.com/${tenantId}/oauth2/v2.0/authorize` +
      `?client_id=${clientId}` +
      `&response_type=code` +
      `&redirect_uri=${encodeURIComponent(redirectUri)}` +
      `&response_mode=query` +
      `&prompt=select_account` +
      `&state=${encodeURIComponent('/')}` +
      '&scope=' +
      encodeURIComponent(
        'openid profile email User.Read Calendars.Read Calendars.ReadWrite Calendars.ReadBasic offline_access',
      );
    // `&code_challenge=${codeChallenge}` +
    // `&code_challenge_method=S256`;
    console.log('authorizationUrl', authorizationUrl);
    //https://login.microsoftonline.com/7b8bb5d4-d35b-418d-8a38-b8de3662569e/oauth2/v2.0/authorize?response_type=code&client_id=24737c09-d9ae-41b4-9c6d-6fb01b2f607e&state=%2F&scope=openid%20profile%20email&redirect_uri=https%3A%2F%2Foauth.pstmn.io%2Fv1%2Fcallback
    res.status(302).redirect(authorizationUrl);
    return;
  }

  @Get('callback')
  async callback(
    @Query('code') code: string,
    @Query('session_state') session_state: string,
    @Req() req: any,
    @Res() res: Response,
  ): Promise<void> {
    //console.log('session_state', session_state);

    // Retrieve the code verifier from the user's session (or secure storage)
    const codeVerifier = req.session.codeVerifier;
    const redirect = req.session.redirect;
    // console.log('codeVerifier', codeVerifier);
    console.log('redirect', redirect);
    if (!redirect) {
      // chance for redirect from web app
      console.log('redirect is not set, redirecting to web app');
      // res.redirect(
      //   'http://localhost:8080/auth/callback?code=' +
      //     code +
      //     '&session_state=' +
      //     session_state,
      // );
      res.send(`
      <html>
        <head>
          <title>Launching App</title>
          <script>
            setTimeout(function() {
              window.location.href = 'http://localhost:8080/auth/callback?code=${code}&session_state=${session_state}';
              setTimeout(function() {
                window.close();
              }, 3000);
            }, 100);
          </script>
        </head>
        <body>
          <p>No redirection occurred. You can close this page.</p>
        </body>
      </html>
    `);
      return;
    }
    // if (!codeVerifier) {
    //   res.status(400).send('Code verifier is missing. Please try again.');
    //   return;
    // }
    const redirectUri =
      this.configService.get<string>('REDIRECT_URI') ??
      req.protocol + '://' + req.get('host') + '/auth/callback';
    // Exchange the authorization code for tokens
    console.log('redirectUri', redirectUri);
    const tokenData = await this.authService.exchangeAuthorizationCodeForToken(
      code,
      codeVerifier,
      redirectUri,
    );

    //  console.log('tokenData', tokenData);

    req.session.tokenData = JSON.stringify(tokenData);

    if (!tokenData) {
      res
        .status(400)
        .send('Failed to retrieve token data. Please try again. ' + code);
      return;
    }

    // Store the tokens and user details in the database
    tokenData.codeVerifier = codeVerifier;
    const newToken =
      await this.authService.storeTokenAndGenerateNewToken(tokenData);

    // Return the new token to the client
    // res.redirect(redirect + '?token=' + newToken);
    let isWebApp = false;
    if (redirect && redirect.startsWith('http')) {
      isWebApp = true;
    }
    if (isWebApp) {
      res.redirect(redirect + '?token=' + newToken);
    } else {
      res.send(`
      <html>
        <head>
          <title>Launching App</title>
          <script>
            setTimeout(function() {
              window.location.href = 'app.sea://oauthredirect?token=${newToken}';
              setTimeout(function() {
                window.close();
              }, 5000);
            }, 100);
          </script>
        </head>
        <body>
          <p>If the app doesn't open, <a href="app.sea://oauthredirect?token=${newToken}">click here</a>. If already opened, you can close this page.</p>
        </body>
      </html>
    `);
    }
    //console.log(`redirect to ${redirect}?token=${newToken}`);

    return;
  }

  @Get('get-fresh-microsoft-token')
  async getFreshMicrosoftToken(
    @Req() req: any,
    @Res() res: Response,
  ): Promise<void> {
    try {
      // Extract JWT token from Authorization header
      const authHeader = req.headers.authorization;
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        res.status(401).json({
          success: false,
          error: 'No authorization token provided',
        });
        return;
      }

      const jwtToken = authHeader.split(' ')[1];
      const decodedToken = jwt.verify(
        jwtToken,
        this.configService.get<string>('TOKEN_SECRET') || '12345',
      ) as any;

      if (
        !decodedToken ||
        typeof decodedToken === 'string' ||
        !decodedToken.userId
      ) {
        res.status(401).json({
          success: false,
          error: 'Invalid token',
        });
        return;
      }

      const userId = decodedToken.userId;
      console.log(` Getting fresh Microsoft token for user ${userId}`);

      // Get the latest token from database (might have been refreshed by middleware)
      const latestToken = await this.authService.findUserOffice365Token(userId);

      if (!latestToken) {
        res.status(404).json({
          success: false,
          error: 'No token record found',
        });
        return;
      }

      // Check if token is still valid
      const currentTime = Math.floor(Date.now() / 1000);
      if (latestToken.expiresIn <= currentTime) {
        // Token expired, try to refresh
        if (
          latestToken.refreshToken &&
          latestToken.refreshToken !== 'not available'
        ) {
          console.log(` Token expired, refreshing for user ${userId}`);

          try {
            const newTokenData = await this.authService.refreshAccessToken(
              latestToken.refreshToken,
            );

            if (newTokenData && newTokenData.access_token) {
              // Update database
              const updateData = {
                accessToken: newTokenData.access_token,
                expiresIn:
                  Math.floor(Date.now() / 1000) +
                  (newTokenData.expires_in || 3600),
                refreshToken:
                  newTokenData.refresh_token || latestToken.refreshToken,
              };

              await this.authService.updateTokenRecord(
                latestToken.id,
                updateData,
              );

              // Return fresh token
              res.json({
                success: true,
                microsoftAccessToken: newTokenData.access_token,
                expiresIn: updateData.expiresIn,
                tokenRefreshed: true,
              });
              return;
            }
          } catch (refreshError) {
            console.log(
              ` Token refresh failed for user ${userId}:`,
              refreshError.message,
            );
          }
        }

        res.status(401).json({
          success: false,
          error: 'Token expired and cannot be refreshed',
        });
        return;
      }

      // Token is still valid, return it
      console.log(` Returning valid Microsoft token for user ${userId}`);
      res.json({
        success: true,
        microsoftAccessToken: latestToken.accessToken,
        expiresIn: latestToken.expiresIn,
        tokenRefreshed: false,
      });
    } catch (error) {
      console.error(`❌ Error getting fresh Microsoft token:`, error.message);
      res.status(500).json({
        success: false,
        error: error.message || 'Failed to get fresh token',
      });
    }
  }
  @Get('access-token')
  async getAccessToken(
    @Req() req: any,
    @Res() res: Response,
    @Query('codeVerifier') codeVerifier: string,
  ): Promise<void> {
    const [accessToken, user, userProfiles] =
      await this.authService.getTokenData(codeVerifier);
    res.json({
      success: true,
      accessToken: accessToken,
      user: user,
      userProfiles: userProfiles,
    });
  }

  @Get('user-profile/all')
  async getAllProfiles(@Res() res: Response): Promise<void> {
    const profileData = await this.authService.getAllProfiles();
    res.json(profileData);
  }

  @Get('user-profile/:personId')
  async getProfileByPersonId(
    @Param('personId') personId: string,
    @Req() req?: any,
  ) {
    personId = req.user.fusionProfile?.PersonId;
    return this.authService.getProfileByPersonId(personId);
  }
  // @Post('login/user')
  // public async loginViaEtisalat(
  //   @Body() body:  EtisalatLoginModel,
  // ): Promise< EtisalatLoginModel> {
  //   // -- not used
  //   const response = await this.authService.loginViaEtisalat();
  //   return response;
  // }

  // @Post('login/user')
  // public async loginViaEtisalat(
  //   @Body() loginRequest: EtisalatLoginModel,
  // ): Promise<EtisalatResponseModel> {
  //   try {
  //     if (!loginRequest.username || !loginRequest.password) {
  //       throw new HttpException(
  //         'Username and password are required',
  //         HttpStatus.BAD_REQUEST,
  //       );
  //     }

  //     const response = await this.authService.loginViaEtisalat(
  //       loginRequest.username,
  //       loginRequest.password,
  //     );

  //     return response;
  //   } catch (error) {
  //     throw new HttpException(
  //       error.message,
  //       error.status || HttpStatus.INTERNAL_SERVER_ERROR,
  //     );
  //   }
  // }
}
