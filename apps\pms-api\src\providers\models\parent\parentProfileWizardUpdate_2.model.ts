// parent.model.ts

export class ParentProfile_2 {
  public fullNameInEnglish?: string;
  public fullNameInArabic?: string;
  public gender?: string;
  public nationality?: string;
  public phoneNumber?: string;
  public email?: string;
  public password?: string;
  public maritalStatusId?: number;
  public isPeopleofDetermination?: boolean;
  public determinationCertificateUpload?: string;
  public profileDocumentUpload?: string;
  public issuingAuthority?: string;
  public dob?: string;
  public parentId?: number;
  public currentWizardNo?: number;
  public currentMenu?: number;
  public showSpouseMenu?: boolean;
}

export class ParentEmploymentInfo_2 {
  public parentId?: number;
  public employerTypeId?: number | null;
  public employerNameId?: number | null;
  public currentEmployerName?: string;
  public letterOfAppointmentUpload?: string;
  public officialEmailId?: string;
  public isWorking?: boolean;
  public area?: string;
  public documentIssueDate?: string;
  public place?: string;
  public address?: string;
  public lattitude?: string;
  public longitude?: string;
  public workLocation?: string;
  public nonEmployedStatus?: number;
  public studentDocument?: string;
}

export class ParentDetails_2 {
  public emiratesDocumentUploadBack?: string;
  public emiratesDocumentUploadFront?: string;
  public passportDocumentUpload?: string;
  public passportNo?: string;
  public passportBirthPlace?: string;
  public passportExpiry?: string;
  public emiratesNo?: string;
  public emiratesExpiry?: string;
  public parentId?: number;
}

export class ParentProfileWizardUpdateModel_2 {
  public isVerifiedByUaePass?: boolean;
  public parentprofile?: ParentProfile_2;
  public parentempinfo?: ParentEmploymentInfo_2;
  public details?: ParentDetails_2;
  public profileReviewStage?: number;
  public familyId?: number;
  public isSpouse?: boolean;
}
