import 'package:dio/dio.dart';
import 'package:seawork/constants/constantTexts.dart';

class LoginRepository {
  final String baseUrl;
  final Dio _dio;

  LoginRepository({required this.baseUrl, required Dio dio}) : _dio = dio {
    // Set the base URL on the Dio instance
    _dio.options.baseUrl = baseUrl;
  }

  // Generate OTP
  Future<Map<String, dynamic>> generateOtp(String phoneNumber) async {
    try {
      print('Generating OTP for phone number: $phoneNumber');

      // Phone number should already be formatted with country code from the UI
      // No need to add hardcoded country code here
      final formattedPhoneNumber = phoneNumber.startsWith('+') ? phoneNumber : '+$phoneNumber';

      print('Formatted phone number: $formattedPhoneNumber');
      print('Making API call to: ${_dio.options.baseUrl}/GenerateOTP');

      final response = await _dio.post(
        '/GenerateOTP',
        data: {'phoneNumber': formattedPhoneNumber},
        options: Options(
          validateStatus: (status) => status! < 500,
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
          },
        ),
      );

      print('API Response Status: ${response.statusCode}');
      print('API Response Data: ${response.data}');

      if (response.statusCode == 200 || response.statusCode == 201) {
        return response.data;
      } else {
        final errorMessage =
            response.data['message'] ?? ConstantTexts.failedToGenerateOtp;
        print('Error response: $errorMessage');
        throw Exception(errorMessage);
      }
    } on DioException catch (e) {
      print('DioError generating OTP: ${e.message}');
      print('DioError type: ${e.type}');
      print('DioError response: ${e.response?.data}');
      throw Exception('${ConstantTexts.errorGeneratingOtp}${e.message}');
    } catch (e) {
      print('Unexpected error generating OTP: $e');
      throw Exception('${ConstantTexts.unexpectedErrorGeneratingOtp}$e');
    }
  }

  // Validate OTP
  Future<Map<String, dynamic>> validateOtp(
      String phoneNumber, String otp) async {
    try {
      // Phone number should already be formatted with country code from the UI
      final formattedPhoneNumber = phoneNumber.startsWith('+') ? phoneNumber : '+$phoneNumber';

      final response = await _dio.post(
        '/ValidateOTP',
        data: {
          'phoneNumber': formattedPhoneNumber,
          'otp': otp,
        },
        options: Options(
          validateStatus: (status) => status! < 500,
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
          },
        ),
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        return response.data;
      } else {
        final errorMessage =
            response.data['message'] ?? ConstantTexts.failedToValidateOtp;
        throw Exception(errorMessage);
      }
    } on DioException catch (e) {
      print('DioError validating OTP: ${e.message}');
      print('DioError type: ${e.type}');
      print('DioError response: ${e.response?.data}');
      throw Exception('${ConstantTexts.errorValidatingOtp}${e.message}');
    } catch (e) {
      print('Unexpected error validating OTP: $e');
      throw Exception('${ConstantTexts.unexpectedErrorValidatingOtp}$e');
    }
  }
}
