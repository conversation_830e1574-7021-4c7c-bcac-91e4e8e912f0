import { Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import Axios, {
  AxiosInstance,
  AxiosRequestConfig,
  AxiosResponse,
  CancelTokenSource,
} from 'axios';
import { RequestContext, tokenVerify } from 'packages/http/request.context';
import { AXIOS_INSTANCE_TOKEN, HTTP_MODULE_OPTIONS } from './http.constants';
import { HttpModuleOptions } from './interfaces';
import { generateOracleJWT } from './oracle.token';

@Injectable()
export class HttpService {
  private authToken: string | null = null;

  constructor(
    @Inject(AXIOS_INSTANCE_TOKEN)
    protected readonly instance: AxiosInstance,
    @Inject(HTTP_MODULE_OPTIONS)
    private readonly options: HttpModuleOptions,
    private readonly configService: ConfigService,
  ) {}

  private async getAuthToken(
    config: HttpModuleOptions,
    tokenSecret: string,
    url: string,
  ): Promise<string | null> {
    const {
      authTokenUrl,
      authTokenParams,
      authTokenContentType,
      authTokenResponseKey,
      headers,
      apiTenant,
    } = config;

    const context = RequestContext.getStore();
    const authorization = context?.authorization;
    const { oracleCustomToken } = authTokenParams || {};
    if (!oracleCustomToken) {
      if (!authTokenParams || !authTokenUrl || !authTokenResponseKey) {
        return null;
      }
    }

    if (authorization || oracleCustomToken) {
      if (!apiTenant) {
        throw new Error('API tenant is not specified in the configuration');
      }
      const token = authorization
        ? authorization.replace(/^Bearer\s+/i, '').trim()
        : null;
      const payload = token ? tokenVerify(token, tokenSecret) : null;
      switch (apiTenant) {
        case 'pms': {
          if (!payload || !payload.pmsProfile) {
            throw new Error('PMS profile not found in token payload');
          }
          const pmsProfile = JSON.parse(payload.pmsProfile || '{}');
          if (!pmsProfile || !pmsProfile.phone) {
            throw new Error('PMS profile phone not found in token payload');
          }
          authTokenParams.userId = '+' + pmsProfile.phone;
          break;
        }
        case 'ems': {
          if (payload && payload.fusionProfile) {
            const emsProfile = JSON.parse(payload.fusionProfile || '{}');
            if (!emsProfile || !emsProfile.UserName) {
              throw new Error(
                'EMS profile userName not found in token payload',
              );
            }

            if (url.indexOf('emps') !== -1) {
              const basicAuthDefault = `Basic ${Buffer.from(`${process.env.EMS_API_USER}:${process.env.EMS_API_PASSWORD}`).toString('base64')}`;
              return basicAuthDefault;
            }

            // if (url.indexOf('tasks') !== -1) {
            const oracleToken = generateOracleJWT(emsProfile.UserName);
            console.log('Generated Oracle Token for EMS:', emsProfile.UserName);
            return oracleToken;
            // const basicAuth = `Basic ${Buffer.from(`${process.env.EMS_API_USER3}:${process.env.EMS_API_PASSWORD3}`).toString('base64')}`;
            // return basicAuth;
            // }
          }
          // if (url.indexOf('approval') !== -1) {
          //   const basicAuth = `Basic ${Buffer.from(`${process.env.EMS_API_USER3}:${process.env.EMS_API_PASSWORD3}`).toString('base64')}`;
          //   return basicAuth;
          // }

          const basicAuthDefault = `Basic ${Buffer.from(`${process.env.EMS_API_USER}:${process.env.EMS_API_PASSWORD}`).toString('base64')}`;
          return basicAuthDefault;
        }
      }
    }
    if (!oracleCustomToken && authTokenUrl && authTokenResponseKey) {
      try {
        console.log('Fetching auth token...');
        console.log('Auth URL:', authTokenUrl);
        console.log('Auth Params:', JSON.stringify(authTokenParams));
        const response = await Axios.post(authTokenUrl, authTokenParams, {
          headers: {
            ...headers,
            ...{
              'Content-Type':
                authTokenContentType || 'application/x-www-form-urlencoded',
            },
          },
        });
        // console.log('Auth token response:', response.data);
        //console.log('Auth token response', response.data.JwtToken);

        this.authToken = response.data[authTokenResponseKey];
        console.log('Auth token:', this.authToken);
        return this.authToken;
      } catch (error) {
        console.error('Error fetching auth token:', error);
        throw error;
      }
    }
    return null;
  }

  private async refreshAuthToken(
    config: HttpModuleOptions,
    tokenSecret: string,
    url: string,
  ): Promise<void> {
    this.authToken = null;
    await this.getAuthToken(config, tokenSecret, url);
  }

  private logRequest(
    method: string,
    url: string,
    params?: any,
    config?: HttpModuleOptions,
  ) {
    let baseURL = this.instance.defaults.baseURL || '';
    if (config?.baseURL) {
      baseURL = config.baseURL;
    }
    const fullUrl = baseURL + '/' + url;
    console.log(`\n\x1b[34m[${method.toUpperCase()}] ${fullUrl}\x1b[0m`);
    if (params) {
      console.log(`\x1b[32m[PARAMS] ${JSON.stringify(params)}\x1b[0m`);
    }

    if (config) {
      console.log(`\x1b[30m[CONFIG] ${JSON.stringify(config)}\x1b[0m`);
    }

    let headers = this.instance.defaults.headers;
    if (config && config.headers) {
      headers = {
        ...headers,
        ...Object.fromEntries(
          // eslint-disable-next-line @typescript-eslint/no-unused-vars
          Object.entries(config.headers).filter(([_, v]) => v !== undefined),
        ),
      };
    }
    console.log(`\x1b[33m[HEADERS] ${JSON.stringify(headers)}\x1b[0m`);
  }

  private logResponse(method: string, url: string, response: AxiosResponse) {
    const fullUrl = response.config.url || url;
    console.log(`\n\x1b[34m[${method.toUpperCase()}]  ${fullUrl}\x1b[0m`);
    console.log(`\x1b[30m[STATUS] ${response.status}\x1b[0m`);
    console.log(`\x1b[32m[HEADERS] ${JSON.stringify(response.headers)}\x1b[0m`);
    console.log(
      `\x1b[33m[DATA] ${JSON.stringify(Object.keys(response.data as Record<string, any>))}\x1b[0m`,
    );
  }

  private logError(method: string, url: string, error: any, config?: any) {
    let fullUrl = this.instance.defaults.baseURL + '/' + url;
    if (config?.baseURL) {
      fullUrl = config.baseURL + '/' + url;
    }

    console.error(
      `\n\x1b[31m[HTTP ERROR] ${method.toUpperCase()} ${fullUrl} - ${error.message}\x1b[0m`,
    );
    if (error.response) {
      console.error(
        `\x1b[31m[ERROR RESPONSE] STATUS: ${error.response.status}, MESSAGE: ${JSON.stringify(error.response.data)}\x1b[0m`,
      );
    }
  }

  get<T = any>(
    url: string,
    params?: any,
    isArray?: boolean,
    config?: HttpModuleOptions,
  ): Promise<T> {
    const baseUrl = this.instance.defaults.baseURL || '';
    if (isArray && baseUrl.indexOf('http://localhost') !== -1) {
      params = { ...params, isArray: true };
    }
    const path = this.argumentsToQuery(url, params);
    return this.makeRequest<T>('GET', path, {}, config);
  }

  delete<T = any, D = any>(
    url: string,
    config?: AxiosRequestConfig<D>,
  ): Promise<T> {
    return this.makeRequest<T>('DELETE', url, {}, config);
  }

  head<T = any, D = any>(
    url: string,
    config?: AxiosRequestConfig<D>,
  ): Promise<T> {
    return this.makeRequest<T>('HEAD', url, {}, config);
  }

  post<T = any, D = any>(
    url: string,
    data?: D,
    config?: AxiosRequestConfig<D> & { postHeaders?: Record<string, string> },
  ): Promise<T>;
  post<T = any>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig,
  ): Promise<T> {
    return this.makeRequest<T>('POST', url, data, config);
  }

  put<T = any, D = any>(
    url: string,
    data?: D,
    config?: AxiosRequestConfig<D> & { postHeaders?: Record<string, string> },
  ): Promise<T>;
  put<T = any>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig,
  ): Promise<T> {
    return this.makeRequest<T>('PUT', url, data, config);
  }

  patch<T = any, D = any>(
    url: string,
    data?: D,
    config?: AxiosRequestConfig<D>,
  ): Promise<T>;
  patch<T = any>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig,
  ): Promise<T> {
    return this.makeRequest<T>('PATCH', url, data, config);
  }

  postForm<T = any, D = any>(
    url: string,
    data?: D,
    config?: AxiosRequestConfig<D>,
  ): Promise<T>;
  postForm<T = any>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig,
  ): Promise<T> {
    return this.makeRequest<T>('POST', url, data, config);
  }

  putForm<T = any, D = any>(
    url: string,
    data?: D,
    config?: AxiosRequestConfig<D>,
  ): Promise<T>;
  putForm<T = any>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig,
  ): Promise<T> {
    return this.makeRequest<T>('PUT', url, data, config);
  }

  patchForm<T = any, D = any>(
    url: string,
    data?: D,
    config?: AxiosRequestConfig<D>,
  ): Promise<T>;
  patchForm<T = any>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig,
  ): Promise<T> {
    return this.makeRequest<T>('PATCH', url, data, config);
  }

  get axiosRef(): AxiosInstance {
    return this.instance;
  }

  private argumentsToQuery(url: string, params?: any): string {
    if (params) {
      console.log(`\x1b[33m[PARAMS] ${JSON.stringify(params)}\x1b[0m`);
      let keys = Object.keys(params);
      // keys are filtered to exclude undefined values
      keys = keys.filter((key) => params[key] !== undefined);
      if (keys.length === 0) {
        return url;
      }
      const filteredParams = keys.map((key) => `${key}=${params[key]}`);
      const query = filteredParams.join('&');
      return `${url}?${query}`;
    }
    return url;
  }

  private handleError(error: any): any {
    const defaultMessage = 'An unexpected error occurred.';
    const defaultDetails = { error: 'Unexpected Error' };
    const defaultStatus = 500;

    if (Axios.isAxiosError(error)) {
      if (error.response) {
        // Server responded with a status code outside 2xx range
        const { status, data } = error.response;
        const message =
          error.message || data?.message || `HTTP ${status} Error`;
        const errorDesc =
          data?.error_description || data?.error || data?.message;
        const details = data || defaultDetails;

        console.error('HTTP Response Error:', {
          status,
          message,
          details,
          errorDesc,
        });

        return {
          statusCode: status,
          message,
          details,
        };
      } else if (error.request) {
        // Request made but no response received
        console.error('HTTP Request Error:', {
          message: 'No response received from the server.',
          request: error.request,
        });

        return {
          statusCode: 503,
          message: 'No response received from the server.',
          details: defaultDetails,
        };
      }
    }

    // Unknown error (not Axios-related)
    console.error('Unexpected Error:', error);

    return {
      statusCode: defaultStatus,
      message: error.message || defaultMessage,
      details: defaultDetails,
    };
  }

  protected async makeRequest<T>(
    method: string,
    url: string,
    data?: any,
    config?: HttpModuleOptions,
  ): Promise<T> {
    method = method.toUpperCase();
    const tokenSecret =
      this.configService.get<string>('TOKEN_SECRET') || '12345';
    if (!config) {
      config = {};
    }

    let cancelSource: CancelTokenSource | undefined;
    if (!config.cancelToken) {
      cancelSource = Axios.CancelToken.source();
      config.cancelToken = cancelSource.token;
    }

    try {
      let headers: any = {};

      if (this.instance.defaults.headers) {
        const extractHeaders = Object.keys(
          this.instance.defaults.headers,
        ).filter(
          (key) => typeof this.instance.defaults.headers[key] === 'string',
        );
        const commonHeaders = Object.keys(
          this.instance.defaults.headers.common,
        ).filter(
          (key) =>
            typeof this.instance.defaults.headers.common[key] === 'string',
        );

        const customHeadersObject = {};
        extractHeaders.forEach((key) => {
          customHeadersObject[key] = this.instance.defaults.headers[key];
        });
        commonHeaders.forEach((key) => {
          customHeadersObject[key] = this.instance.defaults.headers.common[key];
        });
        headers = { ...headers, ...customHeadersObject };
        if (config.headers) {
          headers = { ...headers, ...config.headers };
        }
      } else if (config.headers) {
        headers = { ...headers, ...config.headers };
      }

      if (this.options?.postHeaders && method === 'POST') {
        headers = { ...headers, ...this.options.postHeaders };
      }

      if (config?.postHeaders && method === 'POST') {
        headers = { ...headers, ...config.postHeaders };
      }

      let baseURL = this.instance.defaults.baseURL || '';
      if (config.baseURL) {
        baseURL = config.baseURL;
      }

      if (this.options) {
        config = { ...this.options, ...config };
      }

      config.headers = headers;

      if (config?.authTokenParams) {
        headers = headers || {};
        const authToken = await this.getAuthToken(config, tokenSecret, url);
        if (!authToken) {
          throw new Error(
            'No auth token available from getAuthToken for method: ' +
              method +
              ' url: ' +
              url,
          );
        }
        headers.Authorization =
          authToken.indexOf('Basic') === -1 ? `Bearer ${authToken}` : authToken;
      }

      this.logRequest(method, url, data, config);

      const response: AxiosResponse<T> = await Axios.request({
        method: method.toLowerCase(),
        url: baseURL + '/' + url,
        data,
        ...config,
      });

      this.logResponse(method, url, response);
      return response.data;
    } catch (error) {
      if (
        Axios.isAxiosError(error) &&
        error.response?.status === 401 &&
        this.options?.authTokenParams
      ) {
        await this.refreshAuthToken(config, tokenSecret, url);
        config.headers = config.headers || {};
        config.headers.Authorization = `Bearer ${await this.getAuthToken(config, tokenSecret, url)}`;
        this.logRequest(method, url, data, config);
        const response: AxiosResponse<T> = await Axios.request({
          method,
          url,
          data,
          ...config,
        });
        this.logResponse(method, url, response);
        return response.data;
      } else {
        console.log('Error again:', error);
        const handledError = this.handleError(error);
        this.logError(method, url, handledError, config);
        throw handledError;
      }
    } finally {
      if (config.responseType !== 'stream' && cancelSource) {
        cancelSource.cancel();
      }
    }
  }
}
