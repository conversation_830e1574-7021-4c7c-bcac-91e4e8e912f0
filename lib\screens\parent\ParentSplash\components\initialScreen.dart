import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:seawork/components/commonWidgets/customText.dart';
import 'package:seawork/utils/style/colors.dart';
import 'package:go_router/go_router.dart';
class InitialScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        fit: StackFit.expand,
        children: [
          // Background Image
          SvgPicture.asset(
            'assets/images/Rectangle 3463991 (1).svg', // Replace with your SVG image asset
            height: 150,
            width: 150,
            fit: BoxFit.cover,
          ),
          // Overlay for dark effect
          Container(
            color: Colors.black.withOpacity(0.3),
          ),
          // Content
          Column(
            children: [
              // Logos
              Spacer(),
              SvgPicture.asset(
                'assets/images/logoz.svg', // Replace with your SVG logo asset
                height: 100,
                width: 100,
              ),
              SizedBox(height: MediaQuery.of(context).size.height * 0.30), // Controlled spacing instead of Spacer
              // Updated Get Started Button
              Padding(
                padding: const EdgeInsets.only(left: 20, right: 20),
                child: ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.white,
                    minimumSize: const Size(double.infinity, 56),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8), // Rounded edges
                    ),
                    padding: const EdgeInsets.symmetric(
                        vertical: 20), // Increased padding
                  ),
                  onPressed: () {
                    context.push('/get-start');
                  },
                  child: Center(
                    child: DmSansText(
                      "Get started",
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: AppColors.blackColor,
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 12),
              Padding(
                padding: const EdgeInsets.only(
                  left: 20,
                  right: 20,
                  bottom: 60, // Increased bottom padding to move buttons slightly up
                ),
                child: TextButton(
                  onPressed: () {
                    context.push('/login');
                  },
                  child: DmSansText(
                    "I already have an account",
                    fontSize: 16,
                    fontWeight: FontWeight.w400,
                    color: AppColors.whiteColor,
                    underline: true,
                    decorationColor: Colors.white, // Set underline color to white
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
