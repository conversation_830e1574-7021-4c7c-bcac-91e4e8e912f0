// import { AcademicModule } from './academic.module';
import { NurseryModule } from './nursery.module';
import { PostModule } from './post.module';
// import { ApplicationModule } from './application.module';
import { AreaModule } from './area.module';
// import { AssessmentModule } from './assessment.module';
// import { AuthenticateModule } from './authenticate.module';
// import { DietaryModule } from './dietary.module';
// import { ChatBotModule } from './chatBot.module';
import { PaymentModule } from './payment.module';
// import { CommentModule } from './comment.module';
import { ConversationModule } from './conversation.module';
// import { DailyModule } from './daily.module';
// import { EligibilityModule } from './eligibility.module';
// import { EmergencyModule } from './emergency.module';
// import { RegionModule } from './region.module';
// import { EmployerModule } from './employer.module';
// import { EventModule } from './event.module';
// import { ExceptionModule } from './exception.module';
// import { FeeModule } from './fee.module';
// import { GeneralModule } from './general.module';
// import { AllotmentModule } from './allotment.module';
// import { HealthModule } from './health.module';
// import { MedicalModule } from './medical.module';
// import { MilestoneModule } from './milestone.module';
import { NotificationModule } from './notification.module';
// import { OracleFusionModule } from './oracleFusion.module';
// import { ParentModule } from './parent.module';
// import { ObservationModule } from './observation.module';
// import { ReEnrollmentModule } from './reEnrollment.module';
// import { RefundModule } from './refund.module';
// import { RoleModule } from './role.module';
import { SystemModule } from './system.module';
import { WalletModule } from './wallet.module';
import { EmployerModule } from './employer.module';
// import { TagModule } from './tag.module';
// import { UserModule } from './user.module';
// import { WithdrawalModule } from './withdrawal.module';
export const moduleExports = [
  // AcademicModule,
  NurseryModule,
  PostModule,
  // ApplicationModule,
  AreaModule,
  // AssessmentModule,
  // AuthenticateModule,
  // DietaryModule,
  // ChatBotModule,
  PaymentModule,
  // CommentModule,
  ConversationModule,
  // DailyModule,
  // EligibilityModule,
  // EmergencyModule,
  // RegionModule,
  EmployerModule,
  // EventModule,
  // ExceptionModule,
  // FeeModule,
  // GeneralModule,
  // AllotmentModule,
  // HealthModule,
  // MedicalModule,
  // MilestoneModule,
  NotificationModule,
  // OracleFusionModule,
  // ParentModule,
  // ObservationModule,
  // ReEnrollmentModule,
  // RefundModule,
  // RoleModule,
  SystemModule,
  // TagModule,
  // UserModule,
  // WithdrawalModule,
  WalletModule
];
