import { PaymentService } from '../providers/services/payment.service';
import { Controller, Get, Post, Body, Query, UsePipes, ValidationPipe, Param } from '@nestjs/common';
import { ApiBody, ApiResponse } from '@nestjs/swagger';
import { PaymentReceiptLogModel } from '../providers/models/payment/paymentReceiptLog.model';
import { StudentPaymentRequestModel } from '../providers/models/payment/studentPaymentRequest.model';
import { PaymentViewModelModel } from '../providers/models/payment/paymentViewModel.model';
import { CheckOrderModel } from '../providers/models/payment/checkOrder.model';
import { PaymentModel } from '../providers/models/payment/payment.model';
import { KidInfoModel } from '../providers/models/kid/kidInfo.model';
@Controller()
export class PaymentController {
  constructor(private readonly service: PaymentService) {}

  @Get('nursery/addPaymentReceiptLog')
  @ApiResponse({ type: PaymentReceiptLogModel })
  public async addPaymentReceiptLog(
    @Query() query?: any,
  ): Promise<PaymentReceiptLogModel> {
    //  -- not used
    console.log('No query parameters provided', query);
    const response = await this.service.addPaymentReceiptLog();
    return response;
  }

  @Post('feecollection/getStudentPaymentList')
  @ApiBody({ type: StudentPaymentRequestModel })
  @ApiResponse({ type: StudentPaymentRequestModel })
  public async postStudentPaymentList(
    @Body() body: StudentPaymentRequestModel,
  ): Promise<StudentPaymentRequestModel> {
    // -- not used
    const response = await this.service.postStudentPaymentList(body);
    return response;
  }

  @Post('feecollection/createOfflineMultipleOrder')
  @ApiBody({ type: PaymentViewModelModel })
  @ApiResponse({ type: PaymentViewModelModel })
  public async createOfflineMultipleOrder(
    @Body() body: PaymentViewModelModel,
  ): Promise<PaymentViewModelModel> {
    // -- not used
    const response = await this.service.createOfflineMultipleOrder(body);
    return response;
  }

  @Get('feecollection/getDailyCollectedFee')
  public async getDailyCollectedFee(@Query() query?: any): Promise<any[]> {
    //  -- not used
    const { userId } = query;
    const response = await this.service.getDailyCollectedFee(userId);
    return response;
  }

  @Post('feecollection/getPaymentSummaryList')
  @ApiBody({ type: StudentPaymentRequestModel })
  @ApiResponse({ type: StudentPaymentRequestModel })
  public async postPaymentSummaryList(
    @Body() body: StudentPaymentRequestModel,
  ): Promise<StudentPaymentRequestModel> {
    // -- not used
    const response = await this.service.postPaymentSummaryList(body);
    return response;
  }

  @Get('nursery/getAllPendingPayments')
  @ApiResponse({ type: Array<StudentPaymentRequestModel> })
  public async getAllPendingPayments(
    @Query() query?: any,
  ): Promise<StudentPaymentRequestModel[]> {
    //  -- not used
    const {
      pageNumber,
      pageSize,
      code,
      fullNameInArabic,
      fullNameInEnglish,
      nurseryName,
      gradeName,
    } = query;
    const response = await this.service.getAllPendingPayments(
      pageNumber,
      pageSize,
      code,
      fullNameInArabic,
      fullNameInEnglish,
      nurseryName,
      gradeName,
    );
    return response;
  }

  @Get('payment/testNotification')
  @ApiResponse({ type: PaymentViewModelModel })
  public async testNotification(
    @Query() query?: any,
  ): Promise<PaymentViewModelModel> {
    //  -- not used
    console.log('No query parameters provided', query);
    const response = await this.service.testNotification();
    return response;
  }

  @Post('payment/createOrder')
  @ApiBody({ type: PaymentViewModelModel })
  @ApiResponse({ type: PaymentViewModelModel })
  public async createOrder(@Body() body: any): Promise<PaymentViewModelModel> {
    // -- not used
    const response = await this.service.createOrder(body);
    return response;
  }

  @Post('payment/checkOrder')
  @ApiBody({ type: CheckOrderModel })
  @ApiResponse({ type: CheckOrderModel })
  public async checkOrder(@Body() body: any): Promise<CheckOrderModel> {
    // -- not used
    const response = await this.service.checkOrder(body);
    return response;
  }

  @Post('payment/refreshPaymentStatus')
  @ApiBody({ type: PaymentViewModelModel })
  @ApiResponse({ type: PaymentViewModelModel })
  public async refreshPaymentStatus(
    @Body() body: any,
  ): Promise<PaymentViewModelModel> {
    // -- not used
    const response = await this.service.refreshPaymentStatus(body);
    return response;
  }

  @Get('payment/testReconsilePayments')
  @ApiResponse({ type: PaymentViewModelModel })
  public async testReconsilePayments(
    @Query() query?: any,
  ): Promise<PaymentViewModelModel> {
    //  -- not used
    console.log('No query parameters provided', query);
    const response = await this.service.testReconsilePayments();
    return response;
  }

  @Get('payment/rejectApplicationAfterDueDate20241008')
  @ApiResponse({ type: PaymentViewModelModel })
  public async rejectApplicationAfterDueDate(
    @Query() query?: any,
  ): Promise<PaymentViewModelModel> {
    //  -- not used
    const { academicYearId, isTest } = query;
    const response = await this.service.rejectApplicationAfterDueDate(
      academicYearId,
      isTest,
    );
    return response;
  }

  @Get('payment/rejectApplicationAfterDueDateSendSMSoNLY')
  @ApiResponse({ type: PaymentViewModelModel })
  public async rejectApplicationAfterDueDateSendSMSOnly(
    @Query() query?: any,
  ): Promise<PaymentViewModelModel> {
    //  -- not used
    console.log('No query parameters provided', query);
    const response =
      await this.service.rejectApplicationAfterDueDateSendSMSOnly();
    return response;
  }

  @Post('payment/manualCashPaymentExecution')
  @ApiBody({ type: PaymentViewModelModel })
  @ApiResponse({ type: PaymentViewModelModel })
  public async manualCashPaymentExecution(
    @Body() body: any,
  ): Promise<PaymentViewModelModel> {
    // -- not used
    const response = await this.service.manualCashPaymentExecution(body);
    return response;
  }

  @Post('payment/createMultipleOrder')
  @ApiBody({ type: PaymentViewModelModel })
  @ApiResponse({ type: PaymentViewModelModel })
  public async createMultipleOrder(
    @Body() body: any,
  ): Promise<PaymentViewModelModel> {
    // -- not used
    const response = await this.service.createMultipleOrder(body);
    return response;
  }

  @Post('payment/checkOrderMultiple')
  @ApiBody({ type: CheckOrderModel })
  @ApiResponse({ type: CheckOrderModel })
  public async checkOrderMultiple(@Body() body: any): Promise<CheckOrderModel> {
    // -- not used
    const response = await this.service.checkOrderMultiple(body);
    return response;
  }

  @Post('payment/createMultipleOrderWithWallet')
  @ApiBody({ type: PaymentViewModelModel })
  @ApiResponse({ type: PaymentViewModelModel })
  public async CreateMultipleOrderWithWallet(
    @Body() body: any,
  ): Promise<PaymentViewModelModel> {
    // -- not used
    const response = await this.service.CreateMultipleOrderWithWallet(body);
    return response;
  }

  @Post('payment/checkOrderMultipleWithWallet')
  @ApiBody({ type: CheckOrderModel })
  @ApiResponse({ type: CheckOrderModel })
  public async CheckOrderMultipleWithWallet(
    @Body() body: any,
  ): Promise<CheckOrderModel> {
    // -- not used
    const response = await this.service.CheckOrderMultipleWithWallet(body);
    return response;
  }

  @Get('payment/getpaymenthistorybypaymentId')
  @ApiResponse({ type: PaymentModel })
  public async GetPaymentHistoryByPaymentId(
    @Query() query?: any,
  ): Promise<PaymentModel> {
    //  -- not used
    const { paymentId } = query;
    const response = await this.service.GetPaymentHistoryByPaymentId(paymentId);
    return response;
  }

  @Get('payment/testCheckOrderMultiple')
  @ApiResponse({ type: Array<CheckOrderModel> })
  public async TestCheckOrderMultiple(
    @Query() query?: any,
  ): Promise<CheckOrderModel[]> {
    //  -- not used
    const { telrRefNo } = query;
    const response = await this.service.TestCheckOrderMultiple(telrRefNo);
    return response;
  }

  @Get('payment/sendVacationOptDecemberMessage')
  @ApiResponse({ type: PaymentViewModelModel })
  public async SendVacationOptDecemberMessage(
    @Query() query?: any,
  ): Promise<PaymentViewModelModel> {
    //  -- not used
    console.log('No query parameters provided', query);
    const response = await this.service.SendVacationOptDecemberMessage();
    return response;
  }

  @Get('payment/autoReconcileTransaction')
  @ApiResponse({ type: PaymentViewModelModel })
  public async AutoReconcileTransaction(
    @Query() query?: any,
  ): Promise<PaymentViewModelModel> {
    //  -- not used
    console.log('No query parameters provided', query);
    const response = await this.service.AutoReconcileTransaction();
    return response;
  }

  @Get('payment/issueFixGetOrder')
  @ApiResponse({ type: CheckOrderModel })
  public async IssueFixGetOrder(
    @Query() query?: any,
  ): Promise<CheckOrderModel> {
    //  -- not used
    const { telrRefNo } = query;
    const response = await this.service.IssueFixGetOrder(telrRefNo);
    return response;
  }

  @Get('refunds/getPaymentReceiptDetailsByStudentId')
  @ApiResponse({ type: PaymentReceiptLogModel })
  public async GetPaymentReceiptDetailsByStudentId(
    @Query() query?: any,
  ): Promise<PaymentReceiptLogModel> {
    //  -- not used
    const { studentId, selAcademicYearId } = query;
    const response = await this.service.GetPaymentReceiptDetailsByStudentId(
      studentId,
      selAcademicYearId,
    );
    return response;
  }
    @Get('payment/getKidsDetailsByParentId/:parentId')
@UsePipes(new ValidationPipe({ transform: true }))
@ApiResponse({ type: [KidInfoModel] }) // Note the array brackets for multiple items
public async getKidsDetailsByParentId(
  @Param() params: any,
): Promise<KidInfoModel[]> {
  const { parentId } = params;
  const response = await this.service.getKidsDetailsByParentId(
    parentId,
  );
  return response;
}
}
