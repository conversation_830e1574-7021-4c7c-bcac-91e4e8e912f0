import { Injectable } from '@nestjs/common';
import { HttpService } from 'packages/http';
import { PaymentReceiptLogModel } from '../models/payment/paymentReceiptLog.model';
import { StudentPaymentRequestModel } from '../models/payment/studentPaymentRequest.model';
import { PaymentViewModelModel } from '../models/payment/paymentViewModel.model';
import { CheckOrderModel } from '../models/payment/checkOrder.model';
import { PaymentModel } from '../models/payment/payment.model';
import { KidInfoModel } from '../models/kid/kidInfo.model';
@Injectable()
export class PaymentService {
  constructor(private readonly http: HttpService) {}

  public async addPaymentReceiptLog(): Promise<PaymentReceiptLogModel> {
    // -- not used
    const response = await this.http.get<PaymentReceiptLogModel>(
      `openData/v1/nursery/AddPaymentReceiptLog`,
      {},
      false,
    );
    return response;
  }

  public async postStudentPaymentList(
    model: StudentPaymentRequestModel,
  ): Promise<StudentPaymentRequestModel> {
    // -- not used
    const response = await this.http.post<StudentPaymentRequestModel>(
      `feecollection/GetStudentPaymentList`,
      model,
    );
    return response;
  }

  public async createOfflineMultipleOrder(
    model: PaymentViewModelModel,
  ): Promise<PaymentViewModelModel> {
    // -- not used
    const response = await this.http.post<PaymentViewModelModel>(
      `feecollection/CreateOfflineMultipleOrder`,
      model,
    );
    return response;
  }

  public async getDailyCollectedFee(userId?: number): Promise<any> {
    // -- not used
    const response = await this.http.get(
      `feecollection/GetDailyCollectedFee`,
      { UserId: userId },
      true,
    );
    return response;
  }

  public async postPaymentSummaryList(
    model: StudentPaymentRequestModel,
  ): Promise<StudentPaymentRequestModel> {
    // -- not used
    const response = await this.http.post<StudentPaymentRequestModel>(
      `feecollection/GetPaymentSummaryList`,
      model,
    );
    return response;
  }

  public async getAllPendingPayments(
    pageNumber?: number,
    pageSize?: number,
    code?: string,
    fullNameInArabic?: string,
    fullNameInEnglish?: string,
    nurseryName?: string,
    gradeName?: string,
  ): Promise<StudentPaymentRequestModel[]> {
    // -- not used
    const response = await this.http.get<StudentPaymentRequestModel[]>(
      `nursery/GetAllPendingPayments`,
      {
        PageNumber: pageNumber,
        PageSize: pageSize,
        Code: code,
        FullNameInArabic: fullNameInArabic,
        FullNameInEnglish: fullNameInEnglish,
        NurseryName: nurseryName,
        GradeName: gradeName,
      },
      true,
    );
    return response;
  }

  public async testNotification(): Promise<PaymentViewModelModel> {
    // -- not used
    const response = await this.http.get<PaymentViewModelModel>(
      `payment/TestNotification`,
      {},
      false,
    );
    return response;
  }

  public async createOrder(
    model: PaymentViewModelModel,
  ): Promise<PaymentViewModelModel> {
    // -- not used
    const response = await this.http.post<PaymentViewModelModel>(
      `payment/CreateOrder`,
      model,
    );
    return response;
  }

  public async checkOrder(model: CheckOrderModel): Promise<CheckOrderModel> {
    // -- not used
    const response = await this.http.post<CheckOrderModel>(
      `payment/CheckOrder`,
      model,
    );
    return response;
  }

  public async refreshPaymentStatus(
    model: PaymentViewModelModel,
  ): Promise<PaymentViewModelModel> {
    // -- not used
    const response = await this.http.post<PaymentViewModelModel>(
      `payment/RefreshPaymentStatus`,
      model,
    );
    return response;
  }

  public async testReconsilePayments(): Promise<PaymentViewModelModel> {
    // -- not used
    const response = await this.http.get<PaymentViewModelModel>(
      `payment/TestReconsilePayments`,
      {},
      false,
    );
    return response;
  }

  public async rejectApplicationAfterDueDate(
    academicYearId?: number,
    isTest?: boolean,
  ): Promise<PaymentViewModelModel> {
    // -- not used
    const response = await this.http.get<PaymentViewModelModel>(
      `payment/RejectApplicationAfterDueDate20241008`,
      { AcademicYearId: academicYearId, isTest: isTest },
      false,
    );
    return response;
  }

  public async rejectApplicationAfterDueDateSendSMSOnly(): Promise<PaymentViewModelModel> {
    // -- not used
    const response = await this.http.get<PaymentViewModelModel>(
      `payment/RejectApplicationAfterDueDateSendSMSoNLY`,
      {},
      false,
    );
    return response;
  }

  public async manualCashPaymentExecution(
    model: PaymentViewModelModel,
  ): Promise<PaymentViewModelModel> {
    // -- not used
    const response = await this.http.post<PaymentViewModelModel>(
      `payment/ManualCashPaymentExecution`,
      model,
    );
    return response;
  }

  public async createMultipleOrder(
    model: PaymentViewModelModel,
  ): Promise<PaymentViewModelModel> {
    // -- not used
    const response = await this.http.post<PaymentViewModelModel>(
      `payment/CreateMultipleOrder`,
      model,
    );
    return response;
  }

  public async checkOrderMultiple(
    model: CheckOrderModel,
  ): Promise<CheckOrderModel> {
    // -- not used
    const response = await this.http.post<CheckOrderModel>(
      `payment/CheckOrderMultiple`,
      model,
    );
    return response;
  }

  public async CreateMultipleOrderWithWallet(
    model: PaymentViewModelModel,
  ): Promise<PaymentViewModelModel> {
    // -- not used
    const response = await this.http.post<PaymentViewModelModel>(
      `payment/CreateMultipleOrderWithWallet`,
      model,
    );
    return response;
  }

  public async CheckOrderMultipleWithWallet(
    model: CheckOrderModel,
  ): Promise<CheckOrderModel> {
    // -- not used
    const response = await this.http.post<CheckOrderModel>(
      `payment/CheckOrderMultipleWithWallet`,
      model,
    );
    return response;
  }

  public async GetPaymentHistoryByPaymentId(
    paymentId?: number,
  ): Promise<PaymentModel> {
    // -- not used
    const response = await this.http.get<PaymentModel>(
      `payment/GetpaymenthistorybypaymentId`,
      { PaymentId: paymentId },
      false,
    );
    return response;
  }

  public async TestCheckOrderMultiple(
    telrRefNo?: string,
  ): Promise<CheckOrderModel[]> {
    // -- not used
    const response = await this.http.get<CheckOrderModel[]>(
      `payment/TestCheckOrderMultiple`,
      { TelrRefNo: telrRefNo },
      true,
    );
    return response;
  }

  public async SendVacationOptDecemberMessage(): Promise<PaymentViewModelModel> {
    // -- not used
    const response = await this.http.get<PaymentViewModelModel>(
      `payment/SendVacationOptDecemberMessage`,
      {},
      false,
    );
    return response;
  }

  public async AutoReconcileTransaction(): Promise<PaymentViewModelModel> {
    // -- not used
    const response = await this.http.get<PaymentViewModelModel>(
      `payment/AutoReconcileTransaction`,
      {},
      false,
    );
    return response;
  }

  public async IssueFixGetOrder(telrRefNo?: string): Promise<CheckOrderModel> {
    // -- not used
    const response = await this.http.get<CheckOrderModel>(
      `payment/IssueFixGetOrder`,
      { TelrRefNo: telrRefNo },
      false,
    );
    return response;
  }

  public async GetPaymentReceiptDetailsByStudentId(
    studentId?: number,
    selAcademicYearId?: number,
  ): Promise<PaymentReceiptLogModel> {
    // -- not used
    const response = await this.http.get<PaymentReceiptLogModel>(
      `refunds/GetPaymentReceiptDetailsByStudentId`,
      { StudentId: studentId, SelAcademicYearId: selAcademicYearId },
      false,
    );
    return response;
  }
public async getKidsDetailsByParentId(
    parentId?: number,
    
): Promise<KidInfoModel[]> {
    // -- not used
    const response = await this.http.get<KidInfoModel[]>(
      `payment/GetKidsDetailsByParentId`,
      {
        parentId: parentId,
       
     
      },
      true,
    );
    return response;
}
}
